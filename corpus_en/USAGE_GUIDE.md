# Corpus EN 翻译工具使用指南

## 快速开始

### 1. 运行翻译任务
```bash
cd corpus_en
python3 translate_corpus_en_kimi.py
```

### 2. 测试API连接
```bash
python3 test_kimi_api.py
```

## 配置说明

### API配置
- **密钥**: sk-p0YvkPgFSgMRR1AYFETkag7VzzcyAvRdej0dtI2at7u1D8pn
- **模型**: kimi-k2-0711-preview
- **温度**: 0.6
- **并发**: 1（串行处理）
- **重试**: 不重试

### 数据库配置
- **主机**: 59.110.52.91:13956
- **数据库**: horoscope_prod
- **表**: corpus_en

## 功能特性

### ✅ 已实现的要求
1. **API更换**: 从DeepSeek更换为Kimi API
2. **模型配置**: 使用kimi-k2-0711-preview，温度0.6
3. **并发控制**: 串行处理，并发数量为1
4. **无重试**: 每个字段只调用一次API
5. **JSON键验证**: 翻译后对比原数据，确保键不丢失

### 🔧 核心功能
- **多格式支持**: HTML、JSON、Markdown、纯文本
- **格式保持**: 翻译后保持原始格式结构
- **键结构验证**: JSON翻译后键结构完全一致
- **错误记录**: 详细的错误日志和状态跟踪
- **进度监控**: 实时显示翻译进度

## 测试结果

### 成功测试案例
- **记录ID**: 733
- **字段数**: 29个
- **成功率**: 100%
- **翻译时间**: 16分钟
- **错误数**: 0

### 验证通过项目
✅ API连接正常
✅ 格式保持完整
✅ JSON键结构一致
✅ 无重试策略执行
✅ 错误处理正常

## 文件说明

| 文件名 | 说明 |
|--------|------|
| `translate_corpus_en_kimi.py` | 主翻译脚本 |
| `test_kimi_api.py` | API测试脚本 |
| `translate_corpus_en_kimi.log` | 翻译日志 |
| `translation_errors_kimi.txt` | 错误记录 |
| `README_KIMI.md` | 详细说明文档 |
| `USAGE_GUIDE.md` | 本使用指南 |

## 注意事项

1. **运行前检查**: 确保数据库连接正常
2. **API限制**: 每字段间隔1秒，避免频率限制
3. **数据备份**: 建议翻译前备份数据
4. **监控日志**: 关注日志文件中的错误信息
5. **中断恢复**: 可重新运行，自动跳过已翻译内容

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查网络和数据库配置
2. **API调用失败**: 检查API密钥和网络连接
3. **JSON验证失败**: 查看错误日志了解具体问题

### 日志文件位置
- 运行日志: `translate_corpus_en_kimi.log`
- 错误详情: `translation_errors_kimi.txt`

## 生产环境建议

1. **分批处理**: 可设置limit参数分批处理大量数据
2. **监控设置**: 建议设置日志监控和告警
3. **资源监控**: 监控API调用频率和数据库连接
4. **定期检查**: 定期检查翻译质量和完整性

## 联系支持

如遇问题，请查看日志文件或联系技术支持。
