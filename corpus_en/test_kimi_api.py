#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Kimi API连接和翻译功能
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_kimi_api():
    """测试Kimi API"""
    api_key = 'sk-p0YvkPgFSgMRR1AYFETkag7VzzcyAvRdej0dtI2at7u1D8pn'
    api_url = 'https://api.moonshot.cn/v1/chat/completions'
    
    # 测试文本
    test_texts = {
        'plain_text': '这是一个测试文本，包含占星学术语：太阳星座、月亮星座、上升星座。',
        'json_text': '{"title": "占星报告", "content": "你的太阳星座是白羊座", "planets": {"sun": "白羊座", "moon": "金牛座"}}',
        'html_text': '<div><h1>占星分析</h1><p>你的太阳星座是<strong>白羊座</strong>，月亮星座是<em>金牛座</em>。</p></div>'
    }
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    for test_type, test_text in test_texts.items():
        logger.info(f"\n测试 {test_type}:")
        logger.info(f"原文: {test_text}")
        
        if test_type == 'json_text':
            prompt = f"""请将以下JSON内容中的所有中文翻译成英文。

要求：
1. 保持JSON结构和格式完全不变
2. 只翻译JSON值中的中文部分，不翻译键名
3. 占星学术语使用专业英文词汇
4. 确保翻译后不包含任何中文字符
5. 必须返回严格有效的JSON（双引号、无多余逗号、能被json.loads解析）
6. 只返回翻译后的JSON内容，不要任何解释、额外前后缀或代码块标记

原文：
{test_text}"""
        elif test_type == 'html_text':
            prompt = f"""请将以下HTML富文本内容中的所有中文翻译成英文。

要求：
1. 保持HTML标签和属性完全不变
2. 只翻译标签内的中文文本内容
3. 占星学术语使用专业英文词汇
4. 确保翻译后不包含任何中文字符
5. 只返回翻译后的HTML内容，不要任何解释、不要任何额外前后缀，也不要代码块标记

原文：
{test_text}"""
        else:
            prompt = f"""请将以下中文文本翻译成英文。

要求：
1. 占星学术语使用专业英文词汇
2. 确保翻译后不包含任何中文字符
3. 只返回翻译结果，不要任何解释或额外前后缀

原文：
{test_text}"""
        
        data = {
            'model': 'kimi-k2-0711-preview',
            'messages': [
                {
                    'role': 'system',
                    'content': '你是一个严格的专业中译英翻译器（占星学领域），仅返回目标内容本身，不添加任何解释、前缀、后缀或代码块标记；保持输入的结构与格式（HTML/JSON/Markdown）。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.6,
            'max_tokens': 8000
        }
        
        try:
            response = requests.post(api_url, headers=headers, json=data, timeout=120)
            response.raise_for_status()
            
            result = response.json()
            translated_text = result['choices'][0]['message']['content'].strip()
            
            logger.info(f"译文: {translated_text}")
            
            # 如果是JSON，验证格式
            if test_type == 'json_text':
                try:
                    json.loads(translated_text)
                    logger.info("✓ JSON格式验证通过")
                except json.JSONDecodeError as e:
                    logger.error(f"✗ JSON格式验证失败: {e}")
            
            logger.info("✓ API调用成功")
            
        except requests.exceptions.RequestException as e:
            logger.error(f"✗ API请求失败: {e}")
        except Exception as e:
            logger.error(f"✗ 处理过程出错: {e}")

if __name__ == "__main__":
    test_kimi_api()
