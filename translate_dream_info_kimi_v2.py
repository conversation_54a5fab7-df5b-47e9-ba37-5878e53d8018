#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译dream表的info字段到info_en字段 - 使用Kimi API (分段翻译版本)
"""

import os
import mysql.connector
import json
import re
import time
import logging
from typing import Dict, Any, Optional, List
import requests
from datetime import datetime
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translate_dream_info_kimi_v2.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DreamInfoTranslatorV2:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }
        
        # Kimi API配置 - 使用新的API Key
        self.api_key = 'sk-vND8ZnpfOD4g3DRdbuvLM5pGipgWkZv91Iovs3kUyDL8tlbt'
        self.api_url = 'https://api.moonshot.cn/v1/chat/completions'
        # API请求参数
        self.request_timeout_seconds = 600  # 调整为10分钟
        self.request_max_tokens = 2000
        self.request_temperature = 0.3
        self.retry_sleep_seconds = 2
        
        # 分段参数
        self.max_segment_length = 1000  # 每段最大字符数
        
        # 连接数据库
        self.connection = None
        self.cursor = None

        # 问题记录文件
        self.error_log_file = 'dream_info_translation_errors_kimi_v2.txt'
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def has_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        if not text:
            return False
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))

    def split_html_content(self, html_content: str) -> List[str]:
        """将HTML内容按段落分割成较小的片段"""
        if not html_content:
            return []
        
        # 按<p>标签分割
        paragraphs = html_content.split('<p>')
        
        segments = []
        current_segment = ""
        
        for i, para in enumerate(paragraphs):
            if i == 0:
                # 第一个片段可能包含开头的div标签
                current_segment = para
            else:
                # 重新添加<p>标签
                para_with_tag = '<p>' + para
                
                # 检查当前段落加上现有片段是否超过长度限制
                if len(current_segment + para_with_tag) > self.max_segment_length and current_segment:
                    # 如果超过限制且当前片段不为空，保存当前片段
                    segments.append(current_segment)
                    current_segment = para_with_tag
                else:
                    # 否则添加到当前片段
                    current_segment += para_with_tag
        
        # 添加最后一个片段
        if current_segment:
            segments.append(current_segment)
        
        return segments

    def call_kimi_api(self, text: str) -> Optional[str]:
        """调用Kimi API进行翻译"""
        if not text or not self.has_chinese(text):
            return text

        # 使用用户指定的提示词
        prompt = f"""请将以下HTML富文本内容中的所有中文翻译成英文。

要求：
1. 保持HTML标签和属性完全不变
2. 只翻译标签内的中文文本内容
3. 确保翻译后不包含任何中文字符
4. 只返回翻译后的HTML内容，不要任何解释、不要任何额外前后缀，也不要代码块标记

原文：
{text}"""

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': 'kimi-k2-0711-preview',
            'messages': [
                {
                    'role': 'system',
                    'content': 'You are a professional translator. Translate Chinese to English while preserving HTML structure.'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': self.request_temperature,
            'max_tokens': self.request_max_tokens
        }

        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=self.request_timeout_seconds)
            response.raise_for_status()

            result = response.json()
            translated_text = result['choices'][0]['message']['content'].strip()

            # 清理翻译结果
            translated_text = self.clean_translation_result(translated_text)

            logger.info(f"翻译成功 - 原文长度: {len(text)}, 译文长度: {len(translated_text)}")
            return translated_text

        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"翻译过程出错: {e}")
            return None

    def clean_translation_result(self, text: str) -> str:
        """清理翻译结果，移除不必要的标记"""
        if not text:
            return text

        # 移除代码块标记
        text = re.sub(r'^```html\s*\n?', '', text, flags=re.IGNORECASE)
        text = re.sub(r'^```\s*\n?', '', text, flags=re.IGNORECASE)
        text = re.sub(r'\n?```\s*$', '', text)

        return text.strip()

    def translate_html_content(self, html_content: str) -> Optional[str]:
        """翻译HTML内容（支持分段）"""
        if not html_content or not self.has_chinese(html_content):
            return html_content
        
        # 如果内容较短，直接翻译
        if len(html_content) <= self.max_segment_length:
            return self.call_kimi_api(html_content)
        
        # 分段翻译
        segments = self.split_html_content(html_content)
        logger.info(f"内容分为 {len(segments)} 段进行翻译")
        
        translated_segments = []
        
        for i, segment in enumerate(segments, 1):
            logger.info(f"翻译第 {i}/{len(segments)} 段 (长度: {len(segment)})")
            
            translated_segment = self.call_kimi_api(segment)
            
            if translated_segment is not None:
                translated_segments.append(translated_segment)
                # 段间延迟
                if i < len(segments):
                    time.sleep(1)
            else:
                logger.error(f"第 {i} 段翻译失败")
                return None
        
        # 合并所有翻译后的段落
        return ''.join(translated_segments)

    def get_untranslated_records(self, limit: int = None) -> list:
        """获取未翻译的记录"""
        try:
            # 查找info字段包含中文但info_en字段为空的记录
            query = """
            SELECT id, title, keyword, info, info_en
            FROM dream
            WHERE info IS NOT NULL 
            AND info != '' 
            AND info REGEXP '[\\u4e00-\\u9fff]'
            AND (info_en IS NULL OR info_en = '')
            ORDER BY id
            """

            if limit:
                query += f" LIMIT {limit}"

            self.cursor.execute(query)
            records = self.cursor.fetchall()
            logger.info(f"找到 {len(records)} 条需要翻译的记录")
            return records

        except Exception as e:
            logger.error(f"查询未翻译记录失败: {e}")
            return []

    def log_error_to_file(self, record_id: int, error_msg: str, original_text: str = "", translated_text: str = ""):
        """将错误信息记录到文件"""
        try:
            with open(self.error_log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"\n{'='*80}\n")
                f.write(f"时间: {timestamp}\n")
                f.write(f"记录ID: {record_id}\n")
                f.write(f"错误信息: {error_msg}\n")
                if original_text:
                    f.write(f"原文内容: {original_text[:500]}{'...' if len(original_text) > 500 else ''}\n")
                if translated_text:
                    f.write(f"翻译结果: {translated_text[:500]}{'...' if len(translated_text) > 500 else ''}\n")
                f.write(f"{'='*80}\n")
        except Exception as e:
            logger.error(f"写入错误日志文件失败: {e}")

    def translate_record(self, record: Dict[str, Any]) -> bool:
        """翻译单条记录的info字段"""
        record_id = record['id']
        info_content = record.get('info')
        
        logger.info(f"开始翻译记录 ID: {record_id}")

        if not info_content:
            logger.info(f"记录 {record_id} info字段为空，跳过")
            return True

        if not self.has_chinese(info_content):
            logger.info(f"记录 {record_id} info字段不包含中文，跳过")
            return True

        # 调用分段翻译
        translated_text = self.translate_html_content(info_content)

        if translated_text is not None:
            # 更新数据库
            try:
                update_query = "UPDATE dream SET info_en = %s WHERE id = %s"
                self.cursor.execute(update_query, (translated_text, record_id))
                self.connection.commit()
                
                logger.info(f"记录 {record_id} 翻译完成并保存")
                return True
                
            except Exception as e:
                logger.error(f"保存翻译结果失败: {e}")
                self.log_error_to_file(record_id, f"保存失败: {str(e)}", info_content, translated_text)
                return False
        else:
            logger.error(f"记录 {record_id} 翻译失败")
            self.log_error_to_file(record_id, "翻译失败", info_content)
            return False

    def run_translation(self, limit: int = None):
        """运行翻译任务"""
        if not self.connect_database():
            return

        try:
            # 初始化错误日志文件
            with open(self.error_log_file, 'w', encoding='utf-8') as f:
                f.write(f"Dream Info翻译错误日志 V2 - 开始时间: {datetime.now()}\n")
                f.write("="*80 + "\n")

            # 获取需要翻译的记录
            records = self.get_untranslated_records(limit)

            if not records:
                logger.info("没有需要翻译的记录")
                return

            logger.info(f"开始翻译 {len(records)} 条记录")

            success_count = 0
            failed_count = 0

            for i, record in enumerate(records, 1):
                logger.info(f"处理进度: {i}/{len(records)}")

                try:
                    result = self.translate_record(record)
                    if result:
                        success_count += 1
                    else:
                        failed_count += 1

                    # 每处理1条记录后稍作休息
                    if i < len(records):
                        logger.info(f"已处理 {i} 条记录，休息2秒...")
                        time.sleep(2)

                except Exception as e:
                    logger.error(f"处理记录 {record['id']} 时出错: {e}")
                    self.log_error_to_file(record['id'], f"系统错误: {str(e)}")
                    failed_count += 1
                    continue

            logger.info(f"翻译任务完成！")
            logger.info(f"成功: {success_count}")
            logger.info(f"失败: {failed_count}")

        except Exception as e:
            logger.error(f"翻译任务执行失败: {e}")
        finally:
            self.close_database()

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("开始翻译 dream 表的 info 字段到 info_en 字段 - 使用Kimi API V2")
    logger.info(f"开始时间: {datetime.now()}")
    logger.info("=" * 60)

    translator = DreamInfoTranslatorV2()

    # 测试模式：只处理1条记录
    translator.run_translation(limit=1)

    logger.info(f"结束时间: {datetime.now()}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
