#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译 dream 表中的 info 字段到 info_en 字段 - 使用Kimi API
"""

import os
import mysql.connector
import json
import re
import time
import logging
from typing import Dict, Any, Optional, List
import requests
from datetime import datetime
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translate_dream_info_kimi.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DreamInfoTranslator:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }

        # Kimi API配置 - 使用用户提供的新API Key
        self.api_key = 'sk-vND8ZnpfOD4g3DRdbuvLM5pGipgWkZv91Iovs3kUyDL8tlbt'
        self.api_url = 'https://api.moonshot.cn/v1/chat/completions'

        # API请求参数 - 针对长文本优化
        self.request_timeout_seconds = 600  # 调整为10分钟
        self.request_max_tokens = 16000     # 增加token限制
        self.request_temperature = 0.3      # 降低温度保证翻译质量
        self.retry_sleep_seconds = 10       # 增加重试间隔到10秒

        # 连接数据库
        self.connection = None
        self.cursor = None

        # 问题记录文件
        self.error_log_file = 'dream_info_translation_errors_kimi.txt'

    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False

    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")

    def has_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        if not text:
            return False
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return bool(chinese_pattern.search(text))

    def validate_html(self, text: str) -> tuple[bool, str]:
        """验证HTML格式是否正确"""
        if not text:
            return True, ""

        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(text, 'html.parser')
            # 检查是否包含中文
            if self.has_chinese(text):
                return False, "HTML内容仍包含中文"
            return True, ""
        except Exception as e:
            return False, f"HTML格式错误: {str(e)}"

    def log_error_to_file(self, record_id: int, error_msg: str, original_text: str = "", translated_text: str = ""):
        """将错误信息记录到文件"""
        try:
            with open(self.error_log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"\n{'='*80}\n")
                f.write(f"时间: {timestamp}\n")
                f.write(f"记录ID: {record_id}\n")
                f.write(f"错误信息: {error_msg}\n")
                if original_text:
                    f.write(f"原文内容: {original_text[:1000]}{'...' if len(original_text) > 1000 else ''}\n")
                if translated_text:
                    f.write(f"翻译结果: {translated_text[:1000]}{'...' if len(translated_text) > 1000 else ''}\n")
                f.write(f"{'='*80}\n")
        except Exception as e:
            logger.error(f"写入错误日志文件失败: {e}")

    def call_kimi_api(self, text: str) -> Optional[str]:
        """调用Kimi API进行翻译"""
        if not text or not self.has_chinese(text):
            return text

        # 使用用户指定的提示词
        prompt = f"""请将以下HTML富文本内容中的所有中文翻译成英文。

要求：
1. 保持HTML标签和属性完全不变
2. 只翻译标签内的中文文本内容
3. 确保翻译后不包含任何中文字符
4. 只返回翻译后的HTML内容，不要任何解释、不要任何额外前后缀，也不要代码块标记

原文：
{text}"""

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': 'kimi-k2-0711-preview',
            'messages': [
                {
                    'role': 'system',
                    'content': '你是一个严格的专业中译英翻译器（占星学和周公解梦领域），仅返回目标内容本身，不添加任何解释、前缀、后缀或代码块标记；保持输入的HTML结构与格式。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': self.request_temperature,
            'max_tokens': self.request_max_tokens
        }

        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=self.request_timeout_seconds)
            response.raise_for_status()

            result = response.json()
            translated_text = result['choices'][0]['message']['content'].strip()

            # 清理翻译结果
            translated_text = self.clean_translation_result(translated_text)

            logger.info(f"翻译成功 - 原文长度: {len(text)}, 译文长度: {len(translated_text)}")
            return translated_text

        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"翻译过程出错: {e}")
            return None

    def clean_translation_result(self, text: str) -> str:
        """清理翻译结果，移除不必要的标记"""
        if not text:
            return text

        # 移除代码块标记
        text = re.sub(r'^```html\s*\n?', '', text, flags=re.IGNORECASE)
        text = re.sub(r'^```\s*\n?', '', text, flags=re.IGNORECASE)
        text = re.sub(r'\n?```\s*$', '', text)

        return text.strip()

    def get_untranslated_records(self, limit: int = None) -> list:
        """获取未翻译的记录"""
        try:
            # 查找info字段包含中文且info_en为空的记录
            query = """
            SELECT id, title, info, info_en
            FROM dream
            WHERE info REGEXP '[\\u4e00-\\u9fff]'
            AND (info_en IS NULL OR info_en = '')
            ORDER BY id
            """

            if limit:
                query += f" LIMIT {limit}"

            self.cursor.execute(query)
            records = self.cursor.fetchall()
            logger.info(f"找到 {len(records)} 条需要翻译的记录")
            return records

        except Exception as e:
            logger.error(f"查询未翻译记录失败: {e}")
            return []

    def add_translation_status_columns(self):
        """添加翻译状态跟踪字段"""
        try:
            # 检查字段是否已存在
            self.cursor.execute("""
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'dream'
                AND COLUMN_NAME IN ('translation_status', 'translation_progress', 'translation_updated_at')
            """, (self.db_config['database'],))

            existing_columns = [row['COLUMN_NAME'] for row in self.cursor.fetchall()]

            if 'translation_status' not in existing_columns:
                self.cursor.execute("""
                    ALTER TABLE dream
                    ADD COLUMN translation_status VARCHAR(20) DEFAULT NULL
                """)
                logger.info("添加 translation_status 字段")

            if 'translation_progress' not in existing_columns:
                self.cursor.execute("""
                    ALTER TABLE dream
                    ADD COLUMN translation_progress TEXT DEFAULT NULL
                """)
                logger.info("添加 translation_progress 字段")

            if 'translation_updated_at' not in existing_columns:
                self.cursor.execute("""
                    ALTER TABLE dream
                    ADD COLUMN translation_updated_at TIMESTAMP DEFAULT NULL
                """)
                logger.info("添加 translation_updated_at 字段")

            self.connection.commit()

        except Exception as e:
            logger.error(f"添加翻译状态字段失败: {e}")

    def update_translation_status(self, record_id: int, status: str, progress: str = None):
        """更新翻译状态"""
        try:
            if progress:
                query = """
                UPDATE dream
                SET translation_status = %s, translation_progress = %s,
                    translation_updated_at = NOW()
                WHERE id = %s
                """
                self.cursor.execute(query, (status, progress, record_id))
            else:
                query = """
                UPDATE dream
                SET translation_status = %s, translation_updated_at = NOW()
                WHERE id = %s
                """
                self.cursor.execute(query, (status, record_id))

            self.connection.commit()

        except Exception as e:
            logger.error(f"更新翻译状态失败: {e}")

    def translate_record(self, record: Dict[str, Any]) -> bool:
        """翻译单条记录的info字段"""
        record_id = record['id']
        info_content = record.get('info')

        logger.info(f"开始翻译记录 ID: {record_id}")

        # 标记为处理中
        self.update_translation_status(record_id, 'processing')

        if not info_content:
            logger.info(f"记录 {record_id} info字段为空，跳过")
            self.update_translation_status(record_id, 'completed', 'info_empty')
            return True

        if not self.has_chinese(info_content):
            logger.info(f"记录 {record_id} info字段不包含中文，跳过")
            self.update_translation_status(record_id, 'completed', 'no_chinese')
            return True

        logger.info(f"翻译info字段 (长度: {len(info_content)} 字符)")

        # 调用API翻译
        translated_text = self.call_kimi_api(info_content)

        if translated_text is not None:
            # 验证翻译结果
            is_valid, error_msg = self.validate_html(translated_text)

            if is_valid:
                # 更新数据库
                try:
                    update_query = "UPDATE dream SET info_en = %s WHERE id = %s"
                    self.cursor.execute(update_query, (translated_text, record_id))
                    self.connection.commit()

                    self.update_translation_status(record_id, 'completed', 'translated')
                    logger.info(f"记录 {record_id} 翻译完成并保存")
                    return True

                except Exception as e:
                    logger.error(f"保存翻译结果失败: {e}")
                    self.update_translation_status(record_id, 'failed', f'save_error:{str(e)}')
                    self.log_error_to_file(record_id, f"保存失败: {str(e)}", info_content, translated_text)
                    return False
            else:
                logger.error(f"记录 {record_id} 翻译结果验证失败: {error_msg}")
                self.update_translation_status(record_id, 'failed', f'validation_failed:{error_msg}')
                self.log_error_to_file(record_id, f"验证失败: {error_msg}", info_content, translated_text)
                return False
        else:
            logger.error(f"记录 {record_id} API翻译失败")
            self.update_translation_status(record_id, 'failed', 'api_failed')
            self.log_error_to_file(record_id, "API翻译失败", info_content)
            return False

    def preview_records(self, limit: int = 5):
        """预览需要翻译的记录"""
        try:
            records = self.get_untranslated_records(limit)

            if not records:
                logger.info("没有找到需要翻译的记录")
                return

            logger.info(f"预览前 {len(records)} 条需要翻译的记录：")

            for record in records:
                record_id = record['id']
                title = record['title']
                info_length = len(record['info']) if record['info'] else 0

                logger.info(f"ID: {record_id}, 标题: {title}, Info长度: {info_length}字符")

        except Exception as e:
            logger.error(f"预览记录失败: {e}")

    def run_translation(self, limit: int = None):
        """运行翻译任务"""
        if not self.connect_database():
            return

        try:
            # 初始化错误日志文件
            with open(self.error_log_file, 'w', encoding='utf-8') as f:
                f.write(f"Dream表info字段翻译错误日志 - 开始时间: {datetime.now()}\n")
                f.write("="*80 + "\n")

            # 添加翻译状态跟踪字段
            self.add_translation_status_columns()

            # 预览需要翻译的记录
            self.preview_records()

            # 是否自动继续（默认自动继续）
            auto_confirm = os.environ.get('AUTO_CONFIRM', '1').lower() in ('1', 'true', 'yes', 'y')
            if not auto_confirm:
                print(f"\n是否继续执行翻译操作？(y/n): ", end="")
                user_input = input().strip().lower()
                if user_input not in ['y', 'yes', '是', 'Y']:
                    logger.info("用户取消操作")
                    return

            # 获取需要翻译的记录
            records = self.get_untranslated_records(limit)

            if not records:
                logger.info("没有需要翻译的记录")
                return

            logger.info(f"开始翻译 {len(records)} 条记录")
            logger.info(f"错误日志将记录到: {self.error_log_file}")

            success_count = 0
            failed_count = 0

            for i, record in enumerate(records, 1):
                logger.info(f"处理进度: {i}/{len(records)}")

                try:
                    result = self.translate_record(record)
                    if result:
                        success_count += 1
                    else:
                        failed_count += 1

                    # 每条记录处理后都要休息，确保并发为1，避免API限制
                    logger.info(f"记录 {record['id']} 处理完成，休息60秒后继续...")
                    time.sleep(60)  # 每条记录之间固定间隔60秒，避免API限制

                except Exception as e:
                    logger.error(f"处理记录 {record['id']} 时出错: {e}")
                    self.log_error_to_file(record['id'], f"系统错误: {str(e)}")
                    failed_count += 1
                    continue

            logger.info(f"翻译任务完成！")
            logger.info(f"成功: {success_count}")
            logger.info(f"失败: {failed_count}")
            logger.info(f"错误详情请查看: {self.error_log_file}")

        except Exception as e:
            logger.error(f"翻译任务执行失败: {e}")
        finally:
            self.close_database()

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("开始翻译 dream 表中的 info 字段到 info_en 字段 - 使用Kimi API")
    logger.info(f"开始时间: {datetime.now()}")
    logger.info("=" * 60)

    translator = DreamInfoTranslator()

    # 全量执行翻译任务
    translator.run_translation()  # 处理所有记录

    logger.info(f"结束时间: {datetime.now()}")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
