#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试dream表info字段翻译功能
"""

import mysql.connector
import re
import logging
import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DreamInfoTranslationTester:
    def __init__(self):
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 13956,
            'user': 'horoscope_rwu',
            'password': 'h46eaN6JoQxo0VIe',
            'database': 'horoscope_prod',
            'charset': 'utf8mb4'
        }
        
        # Kimi API配置
        self.api_key = 'sk-vND8ZnpfOD4g3DRdbuvLM5pGipgWkZv91Iovs3kUyDL8tlbt'
        self.api_url = 'https://api.moonshot.cn/v1/chat/completions'
        
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def test_database_connection(self):
        """测试数据库连接"""
        print("\n=== 测试数据库连接 ===")
        
        if not self.connect_database():
            print("✗ 数据库连接失败")
            return False
        
        try:
            # 检查dream表是否存在
            self.cursor.execute("SHOW TABLES LIKE 'dream'")
            table_exists = self.cursor.fetchone()
            
            if table_exists:
                print("✓ dream 表存在")
                
                # 检查表结构
                self.cursor.execute("DESCRIBE dream")
                columns = self.cursor.fetchall()
                
                column_names = [col['Field'] for col in columns]
                print(f"✓ 表字段: {', '.join(column_names)}")
                
                # 检查是否有info_en字段
                has_info_en = 'info_en' in column_names
                
                if has_info_en:
                    print("✓ info_en 字段已存在")
                else:
                    print("✗ info_en 字段不存在，需要先添加该字段")
                    return False
                
                # 获取一些示例数据
                self.cursor.execute("SELECT id, title, LEFT(info, 200) as info_sample, info_en FROM dream LIMIT 3")
                samples = self.cursor.fetchall()
                
                print("\n示例数据:")
                for sample in samples:
                    print(f"ID: {sample['id']}")
                    print(f"  Title: {sample['title']}")
                    print(f"  Info样本: {sample['info_sample'][:100]}...")
                    print(f"  Info_en: {sample['info_en']}")
                    print()
                
                return True
            else:
                print("✗ dream 表不存在")
                return False
                
        except Exception as e:
            print(f"✗ 数据库操作失败: {e}")
            return False
        finally:
            self.close_database()
    
    def test_api_connection(self):
        """测试API连接"""
        print("\n=== 测试Kimi API连接 ===")
        
        test_text = "<p>这是一个测试文本，包含<strong>HTML标签</strong>。</p>"
        
        prompt = f"""请将以下HTML富文本内容中的所有中文翻译成英文。

要求：
1. 保持HTML标签和属性完全不变
2. 只翻译标签内的中文文本内容
3. 占星学术语使用专业英文词汇
4. 确保翻译后不包含任何中文字符
5. 只返回翻译后的HTML内容，不要任何解释、不要任何额外前后缀，也不要代码块标记

原文：
{test_text}"""

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': 'kimi-k2-0711-preview',
            'messages': [
                {
                    'role': 'system',
                    'content': '你是一个严格的专业中译英翻译器（占星学和周公解梦领域），仅返回目标内容本身，不添加任何解释、前缀、后缀或代码块标记；保持输入的HTML结构与格式。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.3,
            'max_tokens': 1000
        }

        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            translated_text = result['choices'][0]['message']['content'].strip()
            
            print("✓ API连接成功")
            print(f"原文: {test_text}")
            print(f"译文: {translated_text}")
            
            # 检查翻译质量
            has_chinese = bool(re.search(r'[\u4e00-\u9fff]+', translated_text))
            has_html_tags = '<' in translated_text and '>' in translated_text
            
            print(f"翻译后是否还有中文: {has_chinese}")
            print(f"是否保持HTML标签: {has_html_tags}")
            
            if not has_chinese and has_html_tags:
                print("✓ 翻译质量测试通过")
                return True
            else:
                print("✗ 翻译质量测试未通过")
                return False

        except requests.exceptions.RequestException as e:
            print(f"✗ API请求失败: {e}")
            return False
        except Exception as e:
            print(f"✗ API测试过程出错: {e}")
            return False
    
    def test_sample_translation(self):
        """测试实际数据翻译"""
        print("\n=== 测试实际数据翻译 ===")
        
        if not self.connect_database():
            print("✗ 数据库连接失败")
            return False
        
        try:
            # 获取一条需要翻译的记录
            self.cursor.execute("""
                SELECT id, title, info
                FROM dream 
                WHERE info REGEXP '[\\u4e00-\\u9fff]' 
                AND (info_en IS NULL OR info_en = '')
                ORDER BY CHAR_LENGTH(info) 
                LIMIT 1
            """)
            
            record = self.cursor.fetchone()
            
            if not record:
                print("✗ 没有找到需要翻译的记录")
                return False
            
            print(f"测试记录 ID: {record['id']}")
            print(f"标题: {record['title']}")
            print(f"Info长度: {len(record['info'])} 字符")
            print(f"Info前200字符: {record['info'][:200]}...")
            
            # 这里只是展示，不实际调用API翻译
            print("✓ 找到测试数据，可以进行翻译")
            return True
            
        except Exception as e:
            print(f"✗ 测试数据获取失败: {e}")
            return False
        finally:
            self.close_database()
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("Dream表info字段翻译功能测试")
        print("=" * 60)
        
        tests = [
            ("数据库连接测试", self.test_database_connection),
            ("API连接测试", self.test_api_connection),
            ("实际数据测试", self.test_sample_translation),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n开始 {test_name}...")
            try:
                result = test_func()
                results.append((test_name, result))
                if result:
                    print(f"✓ {test_name} 通过")
                else:
                    print(f"✗ {test_name} 失败")
            except Exception as e:
                print(f"✗ {test_name} 异常: {e}")
                results.append((test_name, False))
        
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        all_passed = True
        for test_name, result in results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  {test_name}: {status}")
            if not result:
                all_passed = False
        
        if all_passed:
            print("\n🎉 所有测试通过！可以开始翻译任务。")
        else:
            print("\n⚠️  部分测试失败，请检查配置后再运行翻译任务。")
        
        return all_passed

def main():
    """主函数"""
    tester = DreamInfoTranslationTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
