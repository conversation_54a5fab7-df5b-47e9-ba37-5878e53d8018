#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试dream表的info字段内容长度和格式
"""

import mysql.connector
import re

def connect_database():
    """连接数据库"""
    db_config = {
        'host': '************',
        'port': 13956,
        'user': 'horoscope_rwu',
        'password': 'h46eaN6JoQxo0VIe',
        'database': 'horoscope_prod',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor(dictionary=True)
        print("数据库连接成功")
        return connection, cursor
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None, None

def analyze_content():
    """分析内容"""
    connection, cursor = connect_database()
    if not connection:
        return
    
    try:
        # 查看第一条记录的详细内容
        cursor.execute("SELECT id, title, keyword, info FROM dream WHERE id = 1423")
        record = cursor.fetchone()
        
        if record:
            print(f"ID: {record['id']}")
            print(f"Title: {record['title']}")
            print(f"Keyword: {record['keyword']}")
            print(f"Info长度: {len(record['info'])}")
            print(f"Info字符数: {len(record['info'])}")
            
            # 分析内容结构
            info = record['info']
            print(f"\n=== 内容结构分析 ===")
            print(f"包含<div>标签: {'<div>' in info}")
            print(f"包含<p>标签: {'<p>' in info}")
            print(f"包含<strong>标签: {'<strong>' in info}")
            
            # 计算中文字符数
            chinese_chars = re.findall(r'[\u4e00-\u9fff]', info)
            print(f"中文字符数: {len(chinese_chars)}")
            
            # 显示完整内容
            print(f"\n=== 完整内容 ===")
            print(info)
            
            # 尝试分段
            print(f"\n=== 分段分析 ===")
            paragraphs = info.split('<p>')
            print(f"段落数量: {len(paragraphs)}")
            
            for i, para in enumerate(paragraphs[:3]):  # 只显示前3段
                print(f"段落 {i+1} 长度: {len(para)}")
                if len(para) > 200:
                    print(f"段落 {i+1} 内容(前200字符): {para[:200]}...")
                else:
                    print(f"段落 {i+1} 内容: {para}")
                print()
        
    except Exception as e:
        print(f"分析过程出错: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    analyze_content()
